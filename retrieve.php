<?php
include 'db_connect.php';

try {
    $dsn = "mysql:host=$servername;dbname=$dbname;charset=utf8mb4";  // Use dbname instead of servername
    $options = [
        PDO::ATTR_ERRMODE               => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE    => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES      => false,
    ];

    $pdo = new PDO($dsn, $username, $password, $options);

    $stmt = $pdo->prepare("SELECT * FROM tblstudents");
    $stmt->execute();

    $data = $stmt->fetchAll(); 

    echo json_encode($data);
    
} catch (PDOException $e) {
    echo json_encode([
        "error" => $e->getMessage(),  
    ]);
}
?>
