<?php
include 'db_connect.php';

try {
    $dsn = "mysql:host=$servername;dbname=$dbname;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE               => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE    => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES      => false,
    ]);
} catch (PDOException $e) {
    die("Could not connect to the database: " . $e->getMessage());
}

// Fixed isset syntax by replacing `{` with `(`.
if ($_SERVER['REQUEST_METHOD'] == 'GET' && isset($_GET['id'])) {
    $id = $_GET['id'];
    echo getDataById($id);
} elseif ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['id'])) { // Changed $_GET['id'] to $_POST['id'].
    $id = $_POST['id'];
    $studentId = $_POST['studentId'];
    $firstName = $_POST['firstName'];
    $middleName = $_POST['middleName'];
    $lastName = $_POST['lastName'];
    $suffixName = $_POST['suffixName'];

    echo updateData($id, $studentId, $firstName, $middleName, $lastName, $suffixName);
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid request'
    ]);
}

function getDataById($id) {
    global $pdo;

    if (!is_numeric($id) || $id <= 0) {
        // Fixed 'retrun' typo to 'return'.
        return json_encode([
            'success' => false,
            'message' => 'Invalid ID'
        ]);
    }

    $sql = "SELECT * FROM tblstudents WHERE id = :id LIMIT 1";
    $stmt = $pdo->prepare($sql);

    // Fixed syntax: added comma between $id and PDO::PARAM_INT.
    $stmt->bindParam(':id', $id, PDO::PARAM_INT);

    try {
        $stmt->execute();
    } catch (PDOException $e) {
        return json_encode([
            'success' => false,
            'message' => 'Database error: ' . $e->getMessage()
        ]);
    }

    $result = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($result) {
        return json_encode([
            'success' => true,
            'data' => $result // Added the 'data' field to return the result if found.
        ]);
    } else {
        return json_encode([
            'success' => false,
            'message' => 'Data not found'
        ]);
    }
}

function updateData($id, $studentId, $firstName, $middleName, $lastName, $suffixName) {
    global $pdo;

    $sql = "UPDATE tblstudents SET studentId = :studentId, firstName = :firstName, middleName = :middleName, lastName = :lastName, suffixName = :suffixName WHERE id = :id";
    $stmt = $pdo->prepare($sql);

    $stmt->bindParam(':id', $id, PDO::PARAM_INT);
    $stmt->bindParam(':studentId', $studentId, PDO::PARAM_STR);
    $stmt->bindParam(':firstName', $firstName, PDO::PARAM_STR);
    $stmt->bindParam(':middleName', $middleName, PDO::PARAM_STR);
    $stmt->bindParam(':lastName', $lastName, PDO::PARAM_STR);
    $stmt->bindParam(':suffixName', $suffixName, PDO::PARAM_STR);

    try {
        $stmt->execute();
        return json_encode([
            'success' => true,
            'message' => 'Data updated successfully'
        ]);
    } catch (PDOException $e) {
        return json_encode([
            'success' => false,
            'message' => 'Database error: ' . $e->getMessage()
        ]);
    }
}
?>
