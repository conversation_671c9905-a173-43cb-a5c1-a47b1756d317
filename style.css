body {
    font-family: 'Inter', sans-serif;
    background-color: #f4f7f6;
    /* background-color: #666bef; */
    margin: 0;
    padding: 0;
}

h1 {
    font-size: 24px;
    color: #333;
    margin-bottom: 20px;
    text-align: center;
}

/* Form and Table Container */
.table-container, .form-container {
    background-color: #ffffff;
    padding: 30px;
    /* border-radius: 10px; */
    box-shadow: 0px 10px 20px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 800px;
    margin-top: 30px;
    border: none;
    border-top: solid 3px #666bef;
}
.form-container{
    height: 55vh;
}

/* Back Link */
.back-link {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

.back-link a {
    color: #666bef;
    text-decoration: none;
    font-size: 16px;
    text-align: center;
}

.back-link a:hover {
    text-decoration: underline;
}


.links a:hover, .back-link a:hover {
    text-decoration: underline;
}

/* Table Styling for view.php */
table {
    width: 100%;
    margin-top: 30px;
    border-collapse: collapse;
}

th, td {
    padding: 12px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background-color: #f0f0f0;
}

tr:nth-child(even) {
    background-color: #f7f7f7;
}

tr:hover {
    background-color: #f1f1f1;
}

/* Action Links */
.actions a {
    text-decoration: none;
    padding: 5px;
    margin-right: 10px;
}
.actions .edit-link{
    background-color: #666bef;
    color: #f1f1f1;
    border-radius: 5px;
    padding: 8px;
}
.actions .delete-link{
    /* background-color: rgb(220, 11, 11); */
    border: solid 1px #666bef;
    /* color: #f1f1f1; */
    color: #666bef;
    border-radius: 5px;
    padding: 8px;
}

/* Edit Form Styling */
#editForm input {
    width: 100%;
    padding: 10px;
    font-size: 16px;
    margin-bottom: 15px;
    border: 1px solid #ccc;
    border-radius: 5px;
    box-sizing: border-box;
}
#editForm input:focus{
    outline: none;
    border: solid 1px #666bef;
}

#editForm button {
    width: 100%;
    padding: 10px;
    background-color: #666bef;
    color: white;
    font-size: 16px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

#editForm button:hover {
    background-color: #5c60d5;
    color: white;
}

#retrieveDataQueryResult {
    margin-top: 20px;
    text-align: center;
    font-size: 16px;
    color: rgb(227, 32, 32);
}

/* Container Layout for view.php */
.main-container {
    display: flex;
    justify-content: space-between;
    gap: 20px;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
}

.table-container {
    flex: 1;
}

.form-container {
    flex: 0 0 300px;
}