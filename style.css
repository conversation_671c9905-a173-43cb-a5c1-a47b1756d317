body {
    font-family: 'Inter', sans-serif;
    background-color: #f4f7f6;
    /* background-color: #666bef; */
    margin: 0;
    padding: 0;
}

h1 {
    font-size: 24px;
    color: #333;
    margin-bottom: 20px;
    text-align: center;
}

/* Form and Table Container */
.table-container, .form-container {
    background-color: #ffffff;
    padding: 30px;
    /* border-radius: 10px; */
    box-shadow: 0px 10px 20px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 800px;
    margin-top: 30px;
    border: none;
    border-top: solid 3px #666bef;
}
.form-container{
    height: 55vh;
}

/* Back Link */
.back-link {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

.back-link a {
    color: #666bef;
    text-decoration: none;
    font-size: 16px;
    text-align: center;
}

.back-link a:hover {
    text-decoration: underline;
}


.links a:hover, .back-link a:hover {
    text-decoration: underline;
}

/* Table Styling for view.php */
table {
    width: 100%;
    margin-top: 30px;
    border-collapse: collapse;
}

th, td {
    padding: 12px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background-color: #f0f0f0;
}

tr:nth-child(even) {
    background-color: #f7f7f7;
}

tr:hover {
    background-color: #f1f1f1;
}

/* Action Links */
.actions a {
    text-decoration: none;
    padding: 5px;
    margin-right: 10px;
}
.actions .edit-link{
    background-color: #666bef;
    color: #f1f1f1;
    border-radius: 5px;
    padding: 8px;
}
.actions .delete-link{
    /* background-color: rgb(220, 11, 11); */
    border: solid 1px #666bef;
    /* color: #f1f1f1; */
    color: #666bef;
    border-radius: 5px;
    padding: 8px;
}

/* Edit Form Styling */
#editForm input {
    width: 100%;
    padding: 10px;
    font-size: 16px;
    margin-bottom: 15px;
    border: 1px solid #ccc;
    border-radius: 5px;
    box-sizing: border-box;
}
#editForm input:focus{
    outline: none;
    border: solid 1px #666bef;
}

#editForm button {
    width: 100%;
    padding: 10px;
    background-color: #666bef;
    color: white;
    font-size: 16px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

#editForm button:hover {
    background-color: #5c60d5;
    color: white;
}

#retrieveDataQueryResult {
    margin-top: 20px;
    text-align: center;
    font-size: 16px;
    color: rgb(227, 32, 32);
}

/* Container Layout for view.php */
.main-container {
    display: flex;
    justify-content: space-between;
    gap: 20px;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
}

.table-container {
    flex: 1;
}

.form-container {
    flex: 0 0 300px;
}

/* Mobile Responsive Styles */
@media screen and (max-width: 768px) {
    body {
        padding: 10px;
    }

    .main-container {
        flex-direction: column;
        gap: 15px;
        max-width: none;
        margin: 0;
        padding: 0 10px;
    }

    .table-container, .form-container {
        padding: 20px;
        margin-top: 15px;
        max-width: none;
        width: 100%;
        box-sizing: border-box;
    }

    .form-container {
        flex: none;
        height: auto;
        order: -1; /* Move form above table on mobile */
    }

    h1 {
        font-size: 20px;
        margin-bottom: 15px;
    }

    /* Table Responsive Design */
    table {
        font-size: 14px;
        margin-top: 20px;
    }

    th, td {
        padding: 8px 4px;
        font-size: 12px;
    }

    /* Make table horizontally scrollable */
    .table-container {
        overflow-x: auto;
    }

    table {
        min-width: 600px;
        white-space: nowrap;
    }

    /* Action buttons styling for mobile */
    .actions a {
        padding: 6px 8px;
        margin-right: 5px;
        font-size: 12px;
        display: inline-block;
        margin-bottom: 5px;
    }

    /* Form styling for mobile */
    #editForm input {
        padding: 12px;
        font-size: 16px;
        margin-bottom: 12px;
        min-height: 44px;
    }

    #editForm button {
        padding: 12px;
        font-size: 16px;
        min-height: 48px;
    }

    .back-link a {
        font-size: 14px;
    }
}

@media screen and (max-width: 480px) {
    .table-container, .form-container {
        padding: 15px;
        margin-top: 10px;
    }

    h1 {
        font-size: 18px;
    }

    th, td {
        padding: 6px 3px;
        font-size: 11px;
    }

    table {
        min-width: 500px;
    }

    .actions a {
        padding: 4px 6px;
        font-size: 11px;
    }

    #editForm input {
        padding: 10px;
        font-size: 14px;
    }

    #editForm button {
        padding: 10px;
        font-size: 14px;
    }
}

/* Alternative: Card layout for very small screens */
@media screen and (max-width: 400px) {
    /* Hide table headers */
    table thead {
        display: none;
    }

    /* Display table rows as cards */
    table, table tbody, table tr, table td {
        display: block;
        width: 100%;
    }

    table tr {
        border: 1px solid #ddd;
        margin-bottom: 10px;
        padding: 10px;
        border-radius: 5px;
        background-color: #fff;
    }

    table td {
        border: none;
        padding: 5px 0;
        text-align: left;
        position: relative;
        padding-left: 35%;
    }

    table td:before {
        content: attr(data-label) ": ";
        position: absolute;
        left: 0;
        width: 30%;
        font-weight: bold;
        color: #666;
    }

    .actions {
        padding-left: 0 !important;
    }

    .actions:before {
        display: none;
    }
}

/* Toast Message Styles */
.toast-container {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 9999;
    width: 90%;
    max-width: 400px;
}

.toast {
    background-color: #333;
    color: white;
    padding: 16px 20px;
    border-radius: 8px;
    margin-bottom: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    opacity: 0;
    transform: translateY(-20px);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    line-height: 1.4;
}

.toast.show {
    opacity: 1;
    transform: translateY(0);
}

.toast.success {
    background-color: #4CAF50;
    border-left: 4px solid #45a049;
}

.toast.error {
    background-color: #f44336;
    border-left: 4px solid #da190b;
}

.toast.info {
    background-color: #2196F3;
    border-left: 4px solid #1976D2;
}

.toast-icon {
    margin-right: 12px;
    font-size: 16px;
    flex-shrink: 0;
}

.toast-message {
    flex: 1;
}

.toast-close {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 0;
    margin-left: 12px;
    opacity: 0.7;
    transition: opacity 0.2s ease;
}

.toast-close:hover {
    opacity: 1;
}

/* Mobile Toast Adjustments */
@media screen and (max-width: 768px) {
    .toast-container {
        top: 10px;
        width: 95%;
        max-width: none;
    }

    .toast {
        padding: 14px 16px;
        font-size: 13px;
    }

    .toast-icon {
        font-size: 14px;
        margin-right: 10px;
    }
}