<?php
include 'db_connect.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Insert Data with PDO</title>
    <script src="jquery.js"></script>
    <link rel="stylesheet" href="css/fonts.css">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f4f7f6;
            /* background-color: #666bef; */
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }

        .container {
            background-color: #ffffff;
            padding: 30px;
            /* border-radius: 10px; */
            box-shadow: 0px 10px 20px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 450px;
            border: none;
            border-top: solid 3px #666bef;
        }

        h1 {
            font-size: 24px;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }

        label {
            font-size: 16px;
            color: #555;
            margin-bottom: 8px;
            display: block;
        }

        input[type="text"] {
            width: 100%;
            padding: 10px;
            font-size: 16px;
            margin-bottom: 15px;
            border: 1px solid #ccc;
            border-radius: 5px;
            box-sizing: border-box;
            text-transform: uppercase;
        }

        input[type="text"]:focus {
            border-color: #666bef;
            outline: none;
        }

        button {
            background-color: #666bef;
            color: white;
            padding: 10px 20px;
            font-size: 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            width: 100%;
            transition: background-color 0.3s ease;
        }

        button:hover {
            background-color: #5c60d5;

        }

        .links {
            text-align: center;
            margin-top: 20px;
        }

        .links a {
            color: #666bef;
            text-decoration: none;
            font-size: 16px;
        }

        .links a:hover {
            text-decoration: underline;
        }

        #result {
            margin-top: 20px;
            text-align: center;
            font-size: 16px;
            color: #333;
        }

        /* Mobile Responsive Styles */
        @media screen and (max-width: 768px) {
            body {
                padding: 10px;
                height: auto;
                min-height: 100vh;
            }

            .container {
                padding: 20px;
                margin: 10px;
                max-width: none;
                width: calc(100% - 20px);
                box-sizing: border-box;
            }

            h1 {
                font-size: 20px;
                margin-bottom: 15px;
            }

            label {
                font-size: 14px;
                margin-bottom: 6px;
            }

            input[type="text"] {
                padding: 12px;
                font-size: 16px;
                margin-bottom: 12px;
                min-height: 44px;
                box-sizing: border-box;
            }

            button {
                padding: 12px 20px;
                font-size: 16px;
                min-height: 48px;
            }

            .links a {
                font-size: 14px;
            }

            #result {
                font-size: 14px;
            }
        }

        @media screen and (max-width: 480px) {
            .container {
                padding: 15px;
                margin: 5px;
            }

            h1 {
                font-size: 18px;
            }

            input[type="text"] {
                padding: 10px;
                font-size: 14px;
            }

            button {
                padding: 10px 15px;
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Insert Student Data</h1>
        <form id="studentForm">
            <label for="studentId">Student ID:</label>
            <input type="text" id="studentId" name="studentId" required><br>

            <label for="firstName">First Name:</label>
            <input type="text" id="firstName" name="firstName" required><br>

            <label for="middleName">Middle Name:</label>
            <input type="text" id="middleName" name="middleName" placeholder="(if none, type 'N/A')" required><br>

            <label for="lastName">Last Name:</label>
            <input type="text" id="lastName" name="lastName" required><br>

            <label for="suffixName">Suffix Name:</label>
            <input type="text" id="suffixName" name="suffixName"  placeholder="(if none, type 'N/A')" required><br>

            <button type="submit">Submit</button>
        </form>

        <div class="links">
            <a href="view.php">View Records</a>
        </div>

        <div id="result"></div>
    </div>

    <script>
        // JavaScript function to handle form submission via AJAX
        $(document).ready(function() {
            $("#studentForm").on("submit", function(event) {
                event.preventDefault();

                // Get form data
                const formData = {
                    studentId: $("#studentId").val(),
                    firstName: $("#firstName").val(),
                    middleName: $("#middleName").val(),
                    lastName: $("#lastName").val(),
                    suffixName: $("#suffixName").val()
                };

                // Send data to PHP script using AJAX
                $.ajax({
                    url: 'insert.php', // PHP script to handle data insertion
                    type: 'post',
                    data: formData,
                    success: function(response) {
                        $("#result").html(response); // Display result in the result div
                    },
                    error: function(xhr, status, error) {
                        $("#result").html("Error: " + xhr.responseText); // Display error in result div
                    }
                });
            });
        });
    </script>
</body>
</html>
