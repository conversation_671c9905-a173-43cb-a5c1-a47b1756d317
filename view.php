<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Retrieve Data with PDO</title>
    <script src="jquery.js"></script>
    <link rel="stylesheet" href="css/fonts.css">
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <!-- Toast Container -->
    <div class="toast-container" id="toastContainer"></div>

    <div class="main-container">
        <!-- Table Container -->
        <div class="table-container">
            <h1>Student Data</h1>
            <table id="data-table">
                <thead>
                    <tr>
                        <th>Student ID</th>
                        <th>First Name</th>
                        <th>Middle Name</th>
                        <th>Last Name</th>
                        <th>Suffix Name</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Data rows will be appended here -->
                </tbody>
            </table>
            <div id="retrieveDataQueryResult"></div>
        </div>

        <!-- Form Container -->
        <div class="form-container">
            <h1>Edit Student</h1>
            <!-- Edit Form -->
            <form action="" id="editForm">
                <input type="hidden" id="id">
                <input type="text" id="studentId" placeholder="ID Number" required>
                <input type="text" id="firstName" placeholder="First Name" required>
                <input type="text" id="middleName" placeholder="Middle Name" required>
                <input type="text" id="lastName" placeholder="Last Name" required>
                <input type="text" id="suffixName" placeholder="Suffix Name" required>
                <button type="" onclick="updateData()">Update</button>
            </form>
            <div class="back-link">
                <a href="index.php">Back</a>
            </div>
        </div>
    </div>

    <!-- Back Link -->

    <!-- Query Result Message -->

    <script>
        $(document).ready(function(){

            $.ajax({
                url: 'retrieve.php',
                type: 'GET',
                dataType: 'json',
                success: function(data) {
                    if(data.length > 0){
                        var tableBody = $('#data-table tbody');
                        tableBody.empty();

                        $.each(data, function(index, item){
                            var row = $('<tr></tr>');
                            row.append($('<td data-label="Student ID"></td>').text(item.studentId));
                            row.append($('<td data-label="First Name"></td>').text(item.firstName));
                            row.append($('<td data-label="Middle Name"></td>').text(item.middleName));
                            row.append($('<td data-label="Last Name"></td>').text(item.lastName));
                            row.append($('<td data-label="Suffix Name"></td>').text(item.suffixName));

                            var actionCell = $('<td class="actions"></td>');
                            var editLink = $('<a href="#" class="edit-link">Edit</a>');
                            var deleteLink = $('<a href="#" class="delete-link">Delete</a>');

                            editLink.on('click', function(event){
                                event.preventDefault();
                                editData(item.id, row);
                            });

                            deleteLink.on('click', function(event){
                                event.preventDefault();
                                deleteData(item.id, row);
                            });

                            actionCell.append(editLink);
                            actionCell.append(deleteLink);
                            row.append(actionCell);
                            tableBody.append(row);
                        });
                    } else {
                        $("#retrieveDataQueryResult").html("<p>No data found.</p>");
                    }
                }
            });

        });

        function editData(id) {
            $.ajax({
                url: 'update.php',
                method: 'GET',
                data: {id: id},
                dataType: 'json',
                success: function(response){
                    if(response.success){
                        $('#id').val(response.data.id);
                        $('#studentId').val(response.data.studentId);
                        $('#firstName').val(response.data.firstName);
                        $('#middleName').val(response.data.middleName);
                        $('#lastName').val(response.data.lastName);
                        $('#suffixName').val(response.data.suffixName);
                    } else {
                        alert(response.message || "Error retrieving data");
                    }
                },
                error: function(xhr, status, error){
                    alert("AJAX error: " + error);
                }
            });
        }

        function updateData() {
            var updateData = {
                id: $('#id').val(),
                studentId: $('#studentId').val(),
                firstName: $('#firstName').val(),
                middleName: $('#middleName').val(),
                lastName: $('#lastName').val(),
                suffixName: $('#suffixName').val()
            };

            $.ajax({
                url: 'update.php',
                method: 'POST',
                data: updateData,
                dataType: 'json',
                success: function(response){
                    if(response.success){
                        alert("Data updated successfully!");
                        location.reload();
                    } else {
                        alert("Error: Could not update data");
                    }
                },
                error: function(xhr, status, error){
                    alert("AJAX error: " + error);
                }
            });
        }

        function deleteData(id, row) {
            if(confirm("Are you sure you want to delete this record?")){
                $.ajax({
                    url: 'delete.php',
                    type: 'POST',
                    data: {id: id},
                    success: function(response){
                        try{
                            var result = JSON.parse(response);
                            if(result.success){
                                alert("Record deleted successfully!");
                                row.remove();
                            } else {
                                alert('Failed to delete record:' +(result.message || 'Unknown error') );
                            }
                        } catch (e) {
                            alert("Failed to delete record: Invalid server response");
                        }
                    },
                    error: function(xhr, status, error){
                        alert("Failed to delete record: " + error);
                    }
                });
            }
        }
    </script>
</body>
</html>
