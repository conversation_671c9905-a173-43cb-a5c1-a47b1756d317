<?php
include'db_connect.php';

try {
    // Create a new PDO instance
    $pdo = new PDO("mysql:servername=$servername;dbname=$dbname", $username, $password);
    // Set the PDO error mode to exception
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Check if required POST data is set
    if (isset($_POST['studentId']) && isset($_POST['firstName']) && isset($_POST['lastName'])) {
        // Assign POST data to variables
        $studentId = $_POST['studentId'];
        $firstName = $_POST['firstName'];
        $middleName = $_POST['middleName'] ?? ''; // Optional field
        $lastName = $_POST['lastName'];
        $suffixName = $_POST['suffixName'] ?? ''; // Optional field

        // Prepare SQL statement with placeholders
        $sql = "INSERT INTO tblstudents (studentId, firstName, middleName, lastName, suffixName)
                VALUES (:studentId, :firstName, :middleName, :lastName, :suffixName)";

        // Prepare the statement
        $stmt = $pdo->prepare($sql);

        // Bind parameters
        $stmt->bindParam(':studentId', $studentId, PDO::PARAM_STR);
        $stmt->bindParam(':firstName', $firstName, PDO::PARAM_STR);
        $stmt->bindParam(':middleName', $middleName, PDO::PARAM_STR);
        $stmt->bindParam(':lastName', $lastName, PDO::PARAM_STR);
        $stmt->bindParam(':suffixName', $suffixName, PDO::PARAM_STR);

        // Execute the query
        if ($stmt->execute()) {
            // echo" Data Inserted successfully!";
            echo "
            <script>
                alert('Data Inserted successfully!');
                window.location.href = 'index.php';
            </script>
            ";
        } else {
            // echo" Please fill in all required fields";
            echo "
            <script>
                alert('Please fill in all required fields');
                window.location.href = 'index.php';
            </script>
            ";
        }
    }
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage();
}

// Close the connection
$pdo = null;

?>